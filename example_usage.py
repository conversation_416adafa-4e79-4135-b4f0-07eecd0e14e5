import os
import cv2
import numpy as np
from train import load_integrated_model
import time
from pathlib import Path

def sliding_window_detection(image_path, model, window_size=640, overlap=0.2, save_result=True, output_dir='results'):
    """
    使用滑动窗口对大图像进行检测
    
    Args:
        image_path: 图像路径
        model: 加载的模型
        window_size: 滑动窗口大小，默认640x640
        overlap: 窗口重叠比例，默认0.2
        save_result: 是否保存结果
        output_dir: 输出目录
        
    Returns:
        合并后的检测结果
    """
    print(f"🔍 使用滑动窗口进行检测: {image_path}")
    print(f"   📏 窗口大小: {window_size}x{window_size}")
    print(f"   🔄 重叠比例: {overlap*100:.0f}%")
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法读取图像: {image_path}")
        return None
    
    # 获取图像尺寸
    height, width = image.shape[:2]
    print(f"   📊 图像尺寸: {width}x{height}")
    
    # 计算步长
    stride = int(window_size * (1 - overlap))
    
    # 初始化结果列表
    all_detections = []
    all_text_recognitions = []
    
    # 计算窗口数量
    num_windows_x = max(1, int(np.ceil((width - window_size) / stride)) + 1)
    num_windows_y = max(1, int(np.ceil((height - window_size) / stride)) + 1)
    total_windows = num_windows_x * num_windows_y
    
    print(f"   🪟 窗口数量: {total_windows} ({num_windows_x}x{num_windows_y})")
    
    # 创建临时目录保存窗口图像
    temp_dir = os.path.join(output_dir, "temp_windows")
    os.makedirs(temp_dir, exist_ok=True)
    
    # 滑动窗口处理
    window_count = 0
    start_time = time.time()
    
    for y in range(0, height, stride):
        if y + window_size > height:
            y = max(0, height - window_size)  # 确保最后一个窗口不会超出图像边界
            
        for x in range(0, width, stride):
            if x + window_size > width:
                x = max(0, width - window_size)  # 确保最后一个窗口不会超出图像边界
            
            window_count += 1
            print(f"   🪟 处理窗口 {window_count}/{total_windows} - 位置: ({x}, {y})")
            
            # 提取窗口
            window = image[y:y+window_size, x:x+window_size]
            
            # 保存窗口图像
            window_path = os.path.join(temp_dir, f"window_{window_count}.jpg")
            cv2.imwrite(window_path, window)
            
            # 对窗口进行检测
            try:
                result = model.predict(window_path, save_result=False)
                
                # 处理检测结果
                if result and 'detections' in result and len(result['detections']) > 0:
                    detection_result = result['detections'][0]
                    if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                        # 调整检测框坐标
                        for box in detection_result.boxes:
                            # 获取原始坐标
                            box_xyxy = box.xyxy[0].cpu().numpy()
                            # 调整坐标到原始图像
                            adjusted_box = box_xyxy.copy()
                            adjusted_box[0] += x  # x1
                            adjusted_box[1] += y  # y1
                            adjusted_box[2] += x  # x2
                            adjusted_box[3] += y  # y2
                            
                            # 保存调整后的检测结果
                            all_detections.append({
                                'xyxy': adjusted_box,
                                'conf': float(box.conf[0]),
                                'cls': int(box.cls[0]),
                                'cls_name': detection_result.names.get(int(box.cls[0]), f"Class_{int(box.cls[0])}")
                            })
                
                # 处理文本识别结果
                if result and 'text_recognition' in result:
                    for text_result in result['text_recognition']:
                        # 调整文本框坐标
                        bbox = text_result['detection_bbox']
                        adjusted_bbox = [
                            bbox[0] + x,  # x1
                            bbox[1] + y,  # y1
                            bbox[2] + x,  # x2
                            bbox[3] + y   # y2
                        ]
                        
                        # 保存调整后的文本结果
                        all_text_recognitions.append({
                            'text': text_result['text'],
                            'confidence': text_result['confidence'],
                            'detection_bbox': adjusted_bbox,
                            'engine': text_result['engine']
                        })
            
            except Exception as e:
                print(f"   ⚠️ 窗口处理异常: {e}")
    
    # 计算处理时间
    process_time = time.time() - start_time
    print(f"✅ 滑动窗口处理完成，耗时: {process_time:.2f}秒")
    
    # 应用NMS去重
    all_detections = apply_nms_to_detections(all_detections)
    all_text_recognitions = filter_text_results(all_text_recognitions)
    
    # 构建最终结果
    final_result = {
        'image_path': image_path,
        'detection_count': len(all_detections),
        'text_count': len(all_text_recognitions),
        'all_detections': all_detections,
        'text_recognition': all_text_recognitions,
        'model_type': 'SlidingWindowDetection',
        'timestamp': time.time()
    }
    
    # 保存结果
    if save_result:
        save_result_visualization(final_result, image, output_dir)
    
    # 清理临时文件
    for file in os.listdir(temp_dir):
        os.remove(os.path.join(temp_dir, file))
    os.rmdir(temp_dir)
    
    return final_result

def apply_nms_to_detections(detections, iou_threshold=0.5, conf_threshold=0.25):
    """应用非极大值抑制去除重叠的检测框"""
    if not detections:
        return []
    
    # 按类别分组
    detections_by_class = {}
    for det in detections:
        cls = det['cls']
        if cls not in detections_by_class:
            detections_by_class[cls] = []
        detections_by_class[cls].append(det)
    
    # 对每个类别应用NMS
    filtered_detections = []
    for cls, cls_dets in detections_by_class.items():
        # 按置信度排序
        cls_dets.sort(key=lambda x: x['conf'], reverse=True)
        
        # 应用NMS
        keep = []
        for i, det in enumerate(cls_dets):
            if det['conf'] < conf_threshold:
                continue
                
            keep_det = True
            for j in keep:
                if calculate_iou(cls_dets[j]['xyxy'], det['xyxy']) > iou_threshold:
                    keep_det = False
                    break
            
            if keep_det:
                keep.append(i)
        
        # 保留通过NMS的检测框
        for i in keep:
            filtered_detections.append(cls_dets[i])
    
    return filtered_detections

def calculate_iou(box1, box2):
    """计算两个边界框的IoU"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # 计算交集
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    intersection = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

def filter_text_results(text_results, similarity_threshold=0.7):
    """过滤和去重文本识别结果"""
    if not text_results:
        return []
    
    # 按置信度排序
    text_results.sort(key=lambda x: x['confidence'], reverse=True)
    
    # 去重
    filtered_results = []
    for result in text_results:
        is_duplicate = False
        for existing in filtered_results:
            # 计算边界框相似度
            bbox1 = result['detection_bbox']
            bbox2 = existing['detection_bbox']
            
            # 计算IoU
            iou = calculate_iou(bbox1, bbox2)
            
            if iou > similarity_threshold:
                is_duplicate = True
                # 如果新结果置信度更高，替换旧结果
                if result['confidence'] > existing['confidence']:
                    filtered_results.remove(existing)
                    filtered_results.append(result)
                break
        
        if not is_duplicate:
            filtered_results.append(result)
    
    return filtered_results

def save_result_visualization(result, image, output_dir):
    """保存检测结果可视化和JSON数据"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取图像文件名
    image_name = os.path.splitext(os.path.basename(result['image_path']))[0]
    
    # 绘制结果图像
    result_image = image.copy()
    
    # 绘制检测框（绿色）
    for det in result['all_detections']:
        x1, y1, x2, y2 = map(int, det['xyxy'])
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 添加类别标签
        label = f"{det['cls_name']}: {det['conf']:.2f}"
        cv2.putText(result_image, label, (x1, y1-10),
                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 绘制文本框（蓝色）
    for text_result in result['text_recognition']:
        x1, y1, x2, y2 = map(int, text_result['detection_bbox'])
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
        
        # 添加文本标签
        label = f"{text_result['text']}: {text_result['confidence']:.2f}"
        cv2.putText(result_image, label, (x1, y1-10),
                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
    
    # 保存结果图像
    result_image_path = os.path.join(output_dir, f"{image_name}_sliding_result.jpg")
    cv2.imwrite(result_image_path, result_image)
    
    # 保存JSON结果
    result_json_path = os.path.join(output_dir, f"{image_name}_sliding_result.json")
    import json
    
    # 转换numpy数组为列表以便JSON序列化
    serializable_result = {
        'image_path': result['image_path'],
        'detection_count': result['detection_count'],
        'text_count': result['text_count'],
        'all_detections': [
            {
                'xyxy': det['xyxy'].tolist() if hasattr(det['xyxy'], 'tolist') else det['xyxy'],
                'conf': det['conf'],
                'cls': det['cls'],
                'cls_name': det['cls_name']
            } for det in result['all_detections']
        ],
        'text_recognition': result['text_recognition'],
        'model_type': result['model_type'],
        'timestamp': result['timestamp']
    }
    
    with open(result_json_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_result, f, ensure_ascii=False, indent=2)
    
    print(f"💾 结果已保存:")
    print(f"   📸 图像: {result_image_path}")
    print(f"   📄 JSON: {result_json_path}")

def main():
    # 加载整合模型
    print("📂 加载整合模型...")
    model = load_integrated_model('models/integrated_yolo_ocr_model.pt')
    
    if model is None:
        print("❌ 模型加载失败")
        return
    
    # 设置输入图像和输出目录
    image_path = 'DaYuanTuZ_0.png'
    output_dir = 'results'
    
    # 检查图像是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    # 使用滑动窗口进行检测
    result = sliding_window_detection(
        image_path=image_path,
        model=model,
        window_size=640,  # 设置窗口大小为640x640
        overlap=0.2,      # 设置重叠比例为20%
        save_result=True,
        output_dir=output_dir
    )
    
    # 打印结果摘要
    print("\n📊 预测结果摘要:")
    print(f"   🎯 检测到元器件: {result['detection_count']} 个")
    print(f"   📝 识别到文字: {result['text_count']} 段")
    
    # 显示前5个文本结果
    if result['text_count'] > 0:
        print("\n📝 识别到的文字:")
        for i, text in enumerate(result['text_recognition'][:5]):
            print(f"   {i+1}. {text['text']} (置信度: {text['confidence']:.2f})")
        if result['text_count'] > 5:
            print(f"   ... 以及 {result['text_count'] - 5} 个更多文字区域")

if __name__ == "__main__":
    main()