#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合模型使用示例
演示如何创建、保存和加载整合的YOLO+OCR模型
"""

from train import create_multitask_model, save_integrated_model, load_integrated_model
import os

def create_and_save_integrated_model():
    """创建并保存整合模型"""
    print("🚀 创建整合模型...")
    
    # 创建多任务模型
    model = create_multitask_model()
    
    # 保存整合模型
    save_path = 'models/integrated_yolo_ocr_model.pt'
    success = save_integrated_model(model, save_path)
    
    if success:
        print(f"✅ 整合模型已保存到: {save_path}")
        return save_path
    else:
        print("❌ 保存整合模型失败")
        return None

def load_and_predict():
    """加载整合模型并进行预测"""
    print("📂 加载整合模型...")
    
    # 加载整合模型
    model = load_integrated_model('models/integrated_yolo_ocr_model.pt')
    
    if model is None:
        print("❌ 模型加载失败")
        return
    
    # 推理图片
    image_path = 'DaYuanTuZ_0.png'
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    print(f"🎯 开始预测图像: {image_path}")
    result = model.predict(image_path, save_result=True, output_dir='demo_results')
    
    # 打印结果摘要
    print("📊 预测结果摘要:")
    print(f"   🎯 检测到元器件: {result['detection_count']} 个")
    print(f"   📝 识别到文字: {result['text_count']} 段")
    
    # 显示前5个文字识别结果
    for i, text in enumerate(result['text_recognition'][:5]):
        print(f"   {i+1}. {text['text']} (置信度: {text['confidence']:.2f})")

def main():
    """主函数"""
    print("🔧 整合模型使用示例")
    print("=" * 50)
    
    # 检查是否已存在整合模型
    model_path = 'models/integrated_yolo_ocr_model.pt'
    
    if not os.path.exists(model_path):
        print("📝 整合模型不存在，正在创建...")
        created_path = create_and_save_integrated_model()
        if created_path is None:
            return
    else:
        print(f"✅ 发现已存在的整合模型: {model_path}")
    
    print("\n" + "=" * 50)
    print("🎯 开始预测...")
    
    # 加载模型并预测
    load_and_predict()

if __name__ == '__main__':
    main()
