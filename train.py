import warnings
import os
import sys
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import torch.nn.functional as F
import json
import time
import numpy as np
from tqdm import tqdm
from typing import List, Dict, Tuple, Optional
from PIL import Image, ImageDraw, ImageFont

# 设置编码以支持中文显示
import locale
import codecs

# 设置控制台编码
if sys.platform.startswith('win'):
    # Windows系统设置
    os.system('chcp 65001')  # 设置控制台为UTF-8编码
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available. Installing...")
    os.system("pip install easyocr")
    import easyocr
    EASYOCR_AVAILABLE = True

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available. Installing...")
    os.system("pip install cnocr")
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract not available. Installing...")
    os.system("pip install pytesseract")
    import pytesseract
    TESSERACT_AVAILABLE = True

# 移除MMOCR依赖，使用更简单的OCR引擎组合
MMOCR_AVAILABLE = False


def safe_print(text, encoding='utf-8'):
    """安全的打印函数，避免编码错误"""
    try:
        if isinstance(text, bytes):
            text = text.decode(encoding, errors='ignore')
        elif not isinstance(text, str):
            text = str(text)

        # 尝试直接打印
        print(text)
    except UnicodeEncodeError:
        try:
            # 如果直接打印失败，尝试编码为ASCII
            ascii_text = text.encode('ascii', errors='ignore').decode('ascii')
            print(f"[TEXT_ENCODED]: {ascii_text}")
        except Exception:
            # 最后的备用方案
            print("[TEXT_ENCODING_ERROR]: Unable to display text")
    except Exception as e:
        print(f"[PRINT_ERROR]: {e}")


def setup_chinese_support():
    """设置中文支持"""
    try:
        # 设置控制台编码
        if sys.platform.startswith('win'):
            # Windows系统
            try:
                os.system('chcp 65001 >nul 2>&1')  # 静默设置UTF-8编码
            except Exception:
                pass

        # 设置Python编码
        if hasattr(sys.stdout, 'reconfigure'):
            try:
                sys.stdout.reconfigure(encoding='utf-8', errors='ignore')
                sys.stderr.reconfigure(encoding='utf-8', errors='ignore')
            except Exception:
                pass

        safe_print("✅ 中文支持设置完成")

    except Exception as e:
        print(f"Warning: Chinese support setup failed: {e}")


# 初始化中文支持
setup_chinese_support()





class MultiTaskModel(nn.Module):
    """
    简化的多任务模型：YOLO目标检测 + CnOCR文字识别
    架构：YOLO目标检测 + CnOCR文字检测识别
    """
    def __init__(self, yolo_model_path: str, num_classes: int = 47):
        super(MultiTaskModel, self).__init__()

        # 加载预训练YOLO模型
        self.yolo_model = YOLO(yolo_model_path)
        self.num_classes = num_classes

        # 初始化OCR引擎
        self.init_ocr_engines()

        # 置信度阈值 - 进一步降低以提高检测精度
        self.detection_conf_threshold = 0.15  # 降低YOLO检测阈值以检测更多元器件
        self.ocr_confidence_threshold = 0.05  # 进一步降低OCR置信度阈值以检测更多文字



    def init_ocr_engines(self):
        """初始化OCR引擎"""
        self.ocr_engines = {}

        # EasyOCR引擎 - 支持中英文
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                safe_print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                safe_print(f"✗ EasyOCR引擎初始化失败: {e}")

        # CnOCR引擎 - 高精度中文识别
        if CNOCR_AVAILABLE:
            try:
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',
                    det_model_name='db_resnet18',
                    use_gpu=torch.cuda.is_available()
                )
                safe_print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                safe_print(f"✗ CnOCR引擎初始化失败: {e}")

        # Tesseract引擎
        if TESSERACT_AVAILABLE:
            try:
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = pytesseract
                safe_print("✓ Tesseract引擎初始化成功")
            except Exception as e:
                safe_print(f"✗ Tesseract引擎初始化失败: {e}")

        safe_print(f"📊 已初始化 {len(self.ocr_engines)} 个OCR引擎: {list(self.ocr_engines.keys())}")

    def enhance_image_for_detection(self, image):
        """
        增强图像以提高检测精度
        """
        try:
            # 转换为灰度图像进行处理
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 应用CLAHE (对比度限制自适应直方图均衡化)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)

            # 轻微的高斯模糊去噪
            enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)

            # 锐化滤波器
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            enhanced = cv2.filter2D(enhanced, -1, kernel)

            # 转换回BGR格式
            if len(image.shape) == 3:
                enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)

            return enhanced
        except Exception as e:
            safe_print(f"   ⚠️ 图像增强失败: {e}")
            return image

    def detect_objects_multiscale(self, image_path, conf_threshold=None):
        """
        多尺度YOLO目标检测以提高检测精度
        """
        if conf_threshold is None:
            conf_threshold = self.detection_conf_threshold

        safe_print(f"🔍 开始多尺度YOLO检测，置信度阈值: {conf_threshold}")

        all_detections = []
        scales = [0.8, 1.0, 1.2, 1.5]  # 多个缩放比例

        for scale in scales:
            safe_print(f"   检测尺度: {scale}")

            # 对于不同尺度，调整置信度阈值
            scale_conf = conf_threshold * (0.9 if scale != 1.0 else 1.0)

            results = self.yolo_model.predict(
                image_path,
                conf=scale_conf,
                imgsz=int(640 * scale),  # 调整输入图像尺寸
                device='0' if torch.cuda.is_available() else 'cpu',
                verbose=False
            )

            # 收集检测结果
            for result in results:
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy()

                    for box, conf, cls_id in zip(boxes, confidences, class_ids):
                        all_detections.append({
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'class_id': int(cls_id),
                            'scale': scale
                        })

        safe_print(f"🔍 多尺度检测完成，共检测到 {len(all_detections)} 个候选目标")

        # 使用NMS去除重复检测
        final_detections = self.apply_nms_to_detections(all_detections)
        safe_print(f"✅ NMS后保留 {len(final_detections)} 个目标")

        return final_detections

    def apply_nms_to_detections(self, detections, iou_threshold=0.5):
        """
        对检测结果应用非极大值抑制
        """
        if not detections:
            return []

        import numpy as np

        # 按置信度排序
        detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)

        # 转换为numpy数组便于计算
        boxes = np.array([d['bbox'] for d in detections])
        confidences = np.array([d['confidence'] for d in detections])

        # 计算面积
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

        keep = []
        indices = np.arange(len(detections))

        while len(indices) > 0:
            # 选择置信度最高的
            current = indices[0]
            keep.append(current)

            if len(indices) == 1:
                break

            # 计算IoU
            current_box = boxes[current]
            other_boxes = boxes[indices[1:]]

            # 计算交集
            x1 = np.maximum(current_box[0], other_boxes[:, 0])
            y1 = np.maximum(current_box[1], other_boxes[:, 1])
            x2 = np.minimum(current_box[2], other_boxes[:, 2])
            y2 = np.minimum(current_box[3], other_boxes[:, 3])

            intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)
            union = areas[current] + areas[indices[1:]] - intersection

            iou = intersection / union

            # 保留IoU小于阈值的检测
            indices = indices[1:][iou < iou_threshold]

        return [detections[i] for i in keep]

    def detect_objects(self, image_path, conf_threshold=None):
        """
        使用YOLO进行目标检测（包含多尺度检测）
        """
        return self.detect_objects_multiscale(image_path, conf_threshold)



    def extract_text_regions(self, image, detection_results):
        """
        从检测结果中提取可能包含文字的区域
        """
        text_regions = []

        # 安全地检查检测结果
        if len(detection_results) > 0:
            result = detection_results[0]

            # 检查结果是否有boxes属性且不为空
            if hasattr(result, 'boxes') and result.boxes is not None and len(result.boxes) > 0:
                boxes = result.boxes

                for box in boxes:
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 扩展边界框以包含可能的文字区域
                    h, w = image.shape[:2]
                    margin = 10
                    x1 = max(0, int(x1) - margin)
                    y1 = max(0, int(y1) - margin)
                    x2 = min(w, int(x2) + margin)
                    y2 = min(h, int(y2) + margin)

                    # 提取区域
                    region = image[y1:y2, x1:x2]

                    text_regions.append({
                        'region': region,
                        'bbox': (x1, y1, x2, y2),
                        'confidence': confidence,
                        'class_id': class_id
                    })

        return text_regions

    def recognize_text_easyocr(self, image_region):
        """
        使用EasyOCR识别文字
        """
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            results = self.ocr_engines['easyocr'].readtext(image_region)

            text_results = []
            for (bbox, text, confidence) in results:
                if confidence > self.ocr_confidence_threshold:
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'easyocr'
                    })

            return text_results
        except Exception as e:
            print(f"EasyOCR识别错误: {e}")
            return []

    def sliding_window_detection(self, image, window_size=(512, 512), overlap=0.3):
        """
        滑动窗口检测 - 对图像进行分块检测以提高精度
        """
        safe_print(f"🔍 开始滑动窗口检测，窗口大小: {window_size}, 重叠率: {overlap}")

        h, w = image.shape[:2]
        window_h, window_w = window_size

        # 计算步长
        step_h = int(window_h * (1 - overlap))
        step_w = int(window_w * (1 - overlap))

        all_detections = []
        window_count = 0

        # 滑动窗口遍历
        for y in range(0, h - window_h + 1, step_h):
            for x in range(0, w - window_w + 1, step_w):
                # 确保不超出边界
                y_end = min(y + window_h, h)
                x_end = min(x + window_w, w)

                # 提取窗口
                window = image[y:y_end, x:x_end]
                window_count += 1

                print(f"   处理窗口 {window_count}: [{x}:{x_end}, {y}:{y_end}]")

                # 对窗口进行OCR检测
                window_detections = self.detect_text_in_window(window, x, y)
                all_detections.extend(window_detections)

        print(f"🔍 滑动窗口检测完成，共处理 {window_count} 个窗口，检测到 {len(all_detections)} 个文字区域")
        return all_detections

    def detect_text_in_window(self, window, offset_x, offset_y):
        """
        在单个窗口中进行文字检测
        """
        window_detections = []

        # 对每个窗口进行多次检测以提高召回率
        for scale in [0.8, 1.0, 1.2]:  # 多尺度检测
            if scale != 1.0:
                new_h, new_w = int(window.shape[0] * scale), int(window.shape[1] * scale)
                scaled_window = cv2.resize(window, (new_w, new_h))
            else:
                scaled_window = window

            # 使用所有OCR引擎检测
            detections = self.detect_text_with_all_ocr_basic(scaled_window)

            # 调整坐标到原图坐标系
            for detection in detections:
                bbox = detection['bbox']
                if scale != 1.0:
                    # 缩放回原尺寸
                    bbox = [int(coord / scale) for coord in bbox]

                # 添加窗口偏移
                bbox[0] += offset_x  # x1
                bbox[1] += offset_y  # y1
                bbox[2] += offset_x  # x2
                bbox[3] += offset_y  # y2

                detection['bbox'] = bbox
                window_detections.append(detection)

        return window_detections

    def detect_text_with_all_ocr_basic(self, image):
        """
        基础的多引擎OCR检测（不使用滑动窗口）
        """
        all_text_regions = []

        # 使用CnOCR进行检测
        cnocr_regions = self.detect_text_with_cnocr(image)
        all_text_regions.extend(cnocr_regions)

        # 使用EasyOCR进行检测
        easyocr_regions = self.detect_text_with_easyocr(image)
        all_text_regions.extend(easyocr_regions)

        # 使用Tesseract进行检测
        tesseract_regions = self.detect_text_with_tesseract(image)
        all_text_regions.extend(tesseract_regions)

        return all_text_regions

    def detect_text_with_all_ocr(self, image):
        """
        使用所有OCR引擎进行文字检测和识别（包含滑动窗口）
        """
        print("🔍 开始多引擎OCR检测...")

        # 1. 全图检测
        print("📝 进行全图检测...")
        full_image_regions = self.detect_text_with_all_ocr_basic(image)
        print(f"📝 全图检测到 {len(full_image_regions)} 个文字区域")

        # 2. 滑动窗口检测
        print("📝 进行滑动窗口检测...")
        sliding_window_regions = self.sliding_window_detection(image)
        print(f"📝 滑动窗口检测到 {len(sliding_window_regions)} 个文字区域")

        # 3. 合并所有检测结果
        all_text_regions = full_image_regions + sliding_window_regions
        print(f"🔍 总共收集到 {len(all_text_regions)} 个文字区域")

        # 4. 合并和去重
        final_regions = self.ensemble_ocr_results(all_text_regions)
        print(f"✅ 所有OCR引擎检测到 {len(final_regions)} 个文字区域")

        return final_regions

    def detect_text_with_cnocr(self, image):
        """
        使用CnOCR进行全图文字检测和识别
        """
        if 'cnocr' not in self.ocr_engines:
            return []

        try:
            # CnOCR需要PIL图像格式
            from PIL import Image

            # 修复numpy数组判断问题
            if image is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）

            # 增强图像以提高OCR精度
            enhanced_image = self.enhance_image_for_detection(image)
            if hasattr(enhanced_image, 'size') and enhanced_image.size == 0:
                return []
            elif hasattr(enhanced_image, 'shape') and (len(enhanced_image.shape) == 0 or any(dim == 0 for dim in enhanced_image.shape)):
                return []

            # 转换为PIL图像
            if len(enhanced_image.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(enhanced_image, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(enhanced_image)

            # 使用CnOCR进行检测和识别
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            # 调试信息：打印原始结果
            print(f"🔍 CnOCR原始结果数量: {len(results) if results else 0}")

            text_regions = []
            if results:
                for i, result in enumerate(results):
                    text = result.get('text', '')
                    confidence = result.get('score', 0.0)
                    position = result.get('position', [])

                    print(f"   结果 {i+1}: 文字='{text}', 置信度={confidence:.3f}, 位置={position}")

                    if confidence > self.ocr_confidence_threshold and text.strip():
                        # 从position计算bbox
                        if position is not None and hasattr(position, '__len__') and len(position) >= 4:
                            # position格式: [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                            x_coords = [point[0] for point in position]
                            y_coords = [point[1] for point in position]
                            x1, x2 = min(x_coords), max(x_coords)
                            y1, y2 = min(y_coords), max(y_coords)
                            bbox = [int(x1), int(y1), int(x2), int(y2)]

                            text_regions.append({
                                'bbox': bbox,
                                'confidence': confidence,
                                'type': 'cnocr_detection',
                                'text': text,
                                'engine': 'cnocr'
                            })
                            print(f"   ✅ 添加文字区域: '{text}' at {bbox}")
                        else:
                            # 如果没有位置信息，跳过
                            print(f"   ⚠️ 跳过无位置信息的结果: '{text}'")
                            continue
                    else:
                        print(f"   ⚠️ 跳过低置信度或空文字: '{text}' (置信度={confidence:.3f}, 阈值={self.ocr_confidence_threshold})")

            return text_regions
        except Exception as e:
            print(f"CnOCR检测错误: {e}")
            return []

    def detect_text_with_easyocr(self, image):
        """
        使用EasyOCR进行全图文字检测和识别
        """
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            if image is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image, 'size') and image.size == 0:
                return []
            elif hasattr(image, 'shape') and (len(image.shape) == 0 or any(dim == 0 for dim in image.shape)):
                return []

            # 增强图像以提高OCR精度
            enhanced_image = self.enhance_image_for_detection(image)

            # EasyOCR可以直接处理numpy数组，优化参数以提高检测精度
            results = self.ocr_engines['easyocr'].readtext(
                enhanced_image,
                width_ths=0.7,      # 降低宽度阈值以检测更多文字
                height_ths=0.7,     # 降低高度阈值以检测更多文字
                text_threshold=0.7, # 降低文字检测阈值
                low_text=0.4,       # 降低低置信度文字阈值
                link_threshold=0.4, # 降低链接阈值
                canvas_size=2560,   # 增加画布大小以提高精度
                mag_ratio=1.5       # 增加放大比例
            )

            print(f"🔍 EasyOCR原始结果数量: {len(results) if results else 0}")

            text_regions = []
            for i, (bbox, text, confidence) in enumerate(results):
                print(f"   结果 {i+1}: 文字='{text}', 置信度={confidence:.3f}, 位置={bbox}")

                if confidence > self.ocr_confidence_threshold and text.strip():
                    # EasyOCR返回的bbox格式: [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    x1, x2 = min(x_coords), max(x_coords)
                    y1, y2 = min(y_coords), max(y_coords)
                    bbox_rect = [int(x1), int(y1), int(x2), int(y2)]

                    text_regions.append({
                        'bbox': bbox_rect,
                        'confidence': confidence,
                        'type': 'easyocr_detection',
                        'text': text,
                        'engine': 'easyocr'
                    })
                    print(f"   ✅ 添加文字区域: '{text}' at {bbox_rect}")
                else:
                    print(f"   ⚠️ 跳过低置信度或空文字: '{text}' (置信度={confidence:.3f}, 阈值={self.ocr_confidence_threshold})")

            return text_regions
        except Exception as e:
            print(f"EasyOCR检测错误: {e}")
            return []

    def detect_text_with_tesseract(self, image):
        """
        使用Tesseract进行全图文字检测和识别
        """
        if 'tesseract' not in self.ocr_engines:
            return []

        try:
            if image is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image, 'size') and image.size == 0:
                return []
            elif hasattr(image, 'shape') and (len(image.shape) == 0 or any(dim == 0 for dim in image.shape)):
                return []

            # 增强图像以提高OCR精度
            enhanced_image = self.enhance_image_for_detection(image)

            # 配置Tesseract参数 - 支持中英文，优化检测精度
            config = '--oem 3 --psm 6 -l chi_sim+eng -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十接线分段引柜变压器开关电容器母线设备kVAMVa'

            # 获取详细信息包括置信度
            data = pytesseract.image_to_data(enhanced_image, config=config, output_type=pytesseract.Output.DICT)

            print(f"🔍 Tesseract原始结果数量: {len(data['text']) if data else 0}")

            text_regions = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                text_content = data['text'][i].strip()
                confidence = float(data['conf'][i]) / 100.0  # 转换为0-1范围

                if confidence > self.ocr_confidence_threshold and text_content:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    bbox_rect = [int(x), int(y), int(x+w), int(y+h)]

                    text_regions.append({
                        'bbox': bbox_rect,
                        'confidence': confidence,
                        'type': 'tesseract_detection',
                        'text': text_content,
                        'engine': 'tesseract'
                    })
                    print(f"   ✅ 添加文字区域: '{text_content}' at {bbox_rect}")

            return text_regions
        except Exception as e:
            print(f"Tesseract检测错误: {e}")
            return []

    def recognize_text_cnocr(self, image_region):
        """
        使用CnOCR识别文字
        """
        if 'cnocr' not in self.ocr_engines:
            return []

        try:
            # CnOCR需要PIL图像格式
            from PIL import Image

            # 修复numpy数组判断问题
            if image_region is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image_region, 'size') and image_region.size == 0:
                return []
            elif hasattr(image_region, 'shape') and (len(image_region.shape) == 0 or any(dim == 0 for dim in image_region.shape)):
                return []

            # 转换为PIL图像
            if len(image_region.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image_region, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image_region)

            # 使用CnOCR进行识别
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            text_results = []
            for result in results:
                text = result.get('text', '')
                confidence = result.get('score', 0.0)

                if confidence > self.ocr_confidence_threshold and text.strip():
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': result.get('position', []),
                        'engine': 'cnocr'
                    })

            return text_results
        except Exception as e:
            print(f"CnOCR识别错误: {e}")
            return []

    def recognize_text_tesseract(self, image_region):
        """
        使用Tesseract识别文字
        """
        if 'tesseract' not in self.ocr_engines:
            return []

        try:
            # 修复numpy数组判断问题
            if image_region is None:
                return []

            # 检查图像是否为空（对numpy数组使用正确的方法）
            if hasattr(image_region, 'size') and image_region.size == 0:
                return []
            elif hasattr(image_region, 'shape') and (len(image_region.shape) == 0 or any(dim == 0 for dim in image_region.shape)):
                return []

            # 配置Tesseract参数 - 支持中英文
            config = '--oem 3 --psm 6 -l chi_sim+eng'

            # 获取详细信息包括置信度
            data = pytesseract.image_to_data(image_region, config=config, output_type=pytesseract.Output.DICT)

            text_results = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                text_content = data['text'][i].strip()
                confidence = float(data['conf'][i]) / 100.0  # 转换为0-1范围

                if confidence > self.ocr_confidence_threshold and text_content:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    bbox = [[x, y], [x+w, y], [x+w, y+h], [x, y+h]]

                    text_results.append({
                        'text': text_content,
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'tesseract'
                    })

            return text_results
        except Exception as e:
            print(f"Tesseract识别错误: {e}")
            return []

    # MMOCR识别方法已移除 - 安装复杂，使用其他OCR引擎替代
    # def recognize_text_mmocr(self, image_region):
    #     """
    #     使用MMOCR识别文字 (如果可用)
    #     """
    #     if 'mmocr' not in self.ocr_engines:
    #         return []
    #
    #     try:
    #         if isinstance(image_region, type(None)) or image_region.size == 0:
    #             return []
    #
    #         # MMOCR的使用方式较复杂，这里提供基础框架
    #         # 实际使用时需要根据MMOCR的具体API进行调整
    #         text_results = []
    #
    #         # 简化的MMOCR调用示例
    #         # result = mmocr_model(image_region)
    #         # 这里需要根据实际的MMOCR模型进行调整
    #
    #         print("MMOCR识别功能需要进一步配置")
    #         return text_results
    #
    #     except Exception as e:
    #         print(f"MMOCR识别错误: {e}")
    #         return []

    def ensemble_ocr_results(self, all_ocr_results):
        """
        融合多个OCR引擎的结果以提高精度
        支持任意数量的OCR引擎结果
        """
        if not all_ocr_results:
            return []

        # 按置信度排序
        all_ocr_results.sort(key=lambda x: x['confidence'], reverse=True)

        # 去重和融合
        final_results = []
        for result in all_ocr_results:
            # 简单的去重策略：如果文字内容相似度高且位置接近，选择置信度更高的
            is_duplicate = False
            for existing in final_results:
                text_sim = self.text_similarity(result['text'], existing['text'])
                bbox_sim = self.bbox_similarity(result.get('bbox', []), existing.get('bbox', []))

                # 如果文字相似且位置接近，认为是重复
                if text_sim > 0.8 or (text_sim > 0.5 and bbox_sim > 0.7):
                    is_duplicate = True
                    # 如果当前结果置信度更高，替换现有结果
                    if result['confidence'] > existing['confidence']:
                        final_results.remove(existing)
                        final_results.append(result)
                    break

            if not is_duplicate:
                final_results.append(result)

        # 按置信度重新排序
        final_results.sort(key=lambda x: x['confidence'], reverse=True)

        return final_results

    def bbox_similarity(self, bbox1, bbox2):
        """
        计算两个边界框的相似度
        """
        # 检查bbox是否有效
        if bbox1 is None or bbox2 is None:
            return 0.0
        if hasattr(bbox1, '__len__') and len(bbox1) < 4:
            return 0.0
        if hasattr(bbox2, '__len__') and len(bbox2) < 4:
            return 0.0

        try:
            # 计算交集面积
            x1_inter = max(bbox1[0], bbox2[0])
            y1_inter = max(bbox1[1], bbox2[1])
            x2_inter = min(bbox1[2], bbox2[2])
            y2_inter = min(bbox1[3], bbox2[3])

            if x2_inter <= x1_inter or y2_inter <= y1_inter:
                return 0.0

            inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

            # 计算并集面积
            area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
            area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
            union_area = area1 + area2 - inter_area

            return inter_area / union_area if union_area > 0 else 0.0
        except:
            return 0.0

    def text_similarity(self, text1, text2):
        """
        计算两个文本的相似度
        """
        if not text1 or not text2:
            return 0.0

        # 简单的字符级相似度计算
        set1 = set(text1.lower())
        set2 = set(text2.lower())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def predict(self, image_path, save_result=True, output_dir='results'):
        """
        综合预测：目标检测 + OCR文字识别
        使用YOLO目标检测 + CnOCR文字识别
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")

            # 方法1: 使用YOLO进行目标检测
            detection_results = self.detect_objects(image_path)

            # 方法2: 使用所有OCR引擎进行全图文字检测和识别
            try:
                all_ocr_text_regions = self.detect_text_with_all_ocr(image)
                print(f"✅ 所有OCR引擎检测到 {len(all_ocr_text_regions)} 个文字区域")
            except Exception as e:
                print(f"⚠️ OCR文字检测失败: {e}")
                all_ocr_text_regions = []

            # 方法3: 从目标检测结果中提取可能的文字区域
            try:
                detection_text_regions = self.extract_text_regions(image, detection_results)
            except Exception as e:
                print(f"⚠️ 从检测结果提取文字区域失败: {e}")
                detection_text_regions = []
        except Exception as e:
            print(f"⚠️ 预测初始化阶段出错: {e}")
            raise

        # OCR文字识别 - 处理CnOCR检测到的文字和其他区域
        all_text_results = []

        # 处理所有OCR引擎直接检测到的文字（已经包含文字内容）
        for region_info in all_ocr_text_regions:
            if 'text' in region_info and region_info['text'].strip():
                # CnOCR已经包含文字内容，直接添加
                text_result = {
                    'text': region_info['text'],
                    'confidence': region_info['confidence'],
                    'engine': region_info['engine'],
                    'detection_bbox': region_info['bbox'],
                    'detection_confidence': region_info['confidence'],
                    'detection_type': region_info['type'],
                    'detection_class_id': -1
                }
                all_text_results.append(text_result)

        # 处理从目标检测结果提取的文字区域
        for region_info in detection_text_regions:
            if 'region' in region_info:
                region = region_info['region']
            else:
                # 从图像中提取区域
                bbox = region_info['bbox']
                # 安全地解包bbox，处理不同的格式
                if isinstance(bbox, (list, tuple)) and len(bbox) == 4:
                    # 检查是否是简单的(x1, y1, x2, y2)格式
                    if all(isinstance(coord, (int, float)) for coord in bbox):
                        x1, y1, x2, y2 = bbox
                    else:
                        # 可能是EasyOCR格式的四个点坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                        try:
                            # 从四个点计算边界框
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            x1, x2 = min(x_coords), max(x_coords)
                            y1, y2 = min(y_coords), max(y_coords)
                        except (IndexError, TypeError):
                            print(f"⚠️ 无法解析bbox格式: {bbox}")
                            continue
                else:
                    print(f"⚠️ 无效的bbox格式: {bbox}")
                    continue

                # 确保坐标是整数且在图像范围内
                h, w = image.shape[:2]
                x1 = max(0, min(w-1, int(x1)))
                y1 = max(0, min(h-1, int(y1)))
                x2 = max(x1+1, min(w, int(x2)))
                y2 = max(y1+1, min(h, int(y2)))

                region = image[y1:y2, x1:x2]

            # 修复numpy数组判断问题
            if region is None:
                continue
            if hasattr(region, 'size') and region.size == 0:
                continue
            if hasattr(region, 'shape') and (len(region.shape) == 0 or any(dim == 0 for dim in region.shape)):
                continue

            # 使用多个OCR引擎进行识别
            easyocr_results = self.recognize_text_easyocr(region)
            cnocr_results = self.recognize_text_cnocr(region)
            tesseract_results = self.recognize_text_tesseract(region)

            # 融合结果
            all_ocr_results = easyocr_results + cnocr_results + tesseract_results
            ensemble_results = self.ensemble_ocr_results(all_ocr_results)

            # 添加区域信息
            for text_result in ensemble_results:
                # 确保所有数据都是JSON可序列化的
                bbox = region_info['bbox']
                if isinstance(bbox, (tuple, list)):
                    bbox = [int(x) for x in bbox]

                text_result.update({
                    'detection_bbox': bbox,
                    'detection_confidence': float(region_info['confidence']),
                    'detection_type': str(region_info.get('type', 'unknown')),
                    'detection_class_id': int(region_info.get('class_id', -1))
                })

            all_text_results.extend(ensemble_results)

        # 整合结果
        final_result = {
            'image_path': image_path,
            'detections': detection_results,
            'text_recognition': all_text_results,
            'all_ocr_text_regions': len(all_ocr_text_regions),
            'detection_text_regions': len(detection_text_regions),
            'timestamp': time.time()
        }

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result

    def draw_chinese_text(self, img, text, position, font_size=20, color=(255, 0, 0)):
        """使用PIL在图像上绘制中文文字，支持多种字体和编码"""
        try:
            # 确保文本是UTF-8编码的字符串
            if isinstance(text, bytes):
                text = text.decode('utf-8', errors='ignore')
            elif not isinstance(text, str):
                text = str(text)

            # 将OpenCV图像转换为PIL图像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)

            # 尝试加载中文字体，按优先级顺序
            font_paths = [
                # Windows系统字体
                "C:/Windows/Fonts/simhei.ttf",      # 黑体
                "C:/Windows/Fonts/msyh.ttf",        # 微软雅黑
                "C:/Windows/Fonts/simsun.ttc",      # 宋体
                "C:/Windows/Fonts/simkai.ttf",      # 楷体
                "C:/Windows/Fonts/simfang.ttf",     # 仿宋
                # Linux系统字体
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                # macOS系统字体
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/STHeiti Light.ttc",
                # 通用字体
                "arial.ttf",
                "calibri.ttf"
            ]

            font = None
            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        font = ImageFont.truetype(font_path, font_size)
                        break
                except Exception:
                    continue

            # 如果所有字体都加载失败，使用默认字体
            if font is None:
                try:
                    font = ImageFont.load_default()
                except Exception:
                    # 最后的备用方案：使用OpenCV绘制
                    print(f"⚠️ 无法加载任何字体，使用OpenCV绘制: {text}")
                    # 使用ASCII字符替换中文字符以避免乱码
                    ascii_text = text.encode('ascii', errors='ignore').decode('ascii')
                    if not ascii_text.strip():
                        ascii_text = f"Text_{len(text)}chars"
                    cv2.putText(img, ascii_text, position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                    return img

            # 绘制文字（确保颜色格式正确）
            if isinstance(color, tuple) and len(color) == 3:
                # BGR转RGB
                pil_color = (color[2], color[1], color[0])
            else:
                pil_color = color

            draw.text(position, text, font=font, fill=pil_color)

            # 转换回OpenCV格式
            return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)

        except Exception as e:
            print(f"⚠️ 绘制中文文字失败: {e}")
            try:
                # 备用方案：使用ASCII字符
                ascii_text = text.encode('ascii', errors='ignore').decode('ascii')
                if not ascii_text.strip():
                    ascii_text = f"Text_{len(text)}chars"
                cv2.putText(img, ascii_text, position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            except Exception as e2:
                print(f"⚠️ 备用绘制方案也失败: {e2}")
                # 最终备用：绘制一个简单的标记
                cv2.putText(img, "TEXT", position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            return img

    def save_prediction_result(self, result, image, output_dir):
        """
        保存预测结果（可视化图像和JSON数据）
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取文件名
        image_name = Path(result['image_path']).stem

        # 在图像上绘制结果
        result_image = image.copy()

        # 绘制目标检测框 - 绿色框住元器件
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                boxes = detection_result.boxes
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])

                    # 绘制元器件检测框 - 绿色 (BGR格式: 0,255,0)
                    cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(result_image, f'Class:{class_id} {confidence:.2f}',
                               (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制OCR结果 - 蓝色框住文字
        for text_result in result['text_recognition']:
            bbox = text_result['detection_bbox']
            text = text_result['text']
            confidence = text_result['confidence']

            # 绘制文字区域 - 蓝色 (BGR格式: 255,0,0)
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 0), 2)

            # 使用支持中文的方式添加识别的文字 - 蓝色
            text_to_show = f'{text} ({confidence:.2f})'
            result_image = self.draw_chinese_text(result_image, text_to_show,
                                                (bbox[0], bbox[3]+20), font_size=16, color=(255, 0, 0))

        # 保存可视化结果
        cv2.imwrite(os.path.join(output_dir, f'{image_name}_result.jpg'), result_image)

        # 保存JSON结果
        detections_list = []
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                detections_list = [
                    {
                        'bbox': box.xyxy[0].cpu().numpy().tolist(),
                        'confidence': float(box.conf[0]),
                        'class_id': int(box.cls[0])
                    } for box in detection_result.boxes
                ]

        json_result = {
            'image_path': result['image_path'],
            'detections': detections_list,
            'text_recognition': result['text_recognition'],
            'timestamp': result['timestamp']
        }

        with open(os.path.join(output_dir, f'{image_name}_result.json'), 'w', encoding='utf-8') as f:
            json.dump(json_result, f, ensure_ascii=False, indent=2, default=self._json_serializer)

    def _json_serializer(self, obj):
        """
        JSON序列化辅助函数，处理numpy类型
        """
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'item'):  # torch tensor
            return obj.item()
        elif hasattr(obj, 'tolist'):  # torch tensor
            return obj.tolist()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def train_yolo_model():
    """
    训练YOLO目标检测模型
    """
    print("🚀 开始训练YOLO目标检测模型...")

    # 检查GPU可用性
    device = '0' if torch.cuda.is_available() else 'cpu'
    print(f"📱 使用设备: {'GPU' if device == '0' else 'CPU'}")

    # 加载预训练模型
    model = YOLO(r'yolo11s.pt')

    # 高精度训练参数配置
    training_args = {
        'data': r'yqjdataset/data.yaml',
        'epochs': 3,  # 增加训练轮数以提高精度
        'imgsz': 640,
        'batch': 64,
        'device': device,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,  # 最终学习率
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'close_mosaic': 15,
        'workers': 4,
        'amp': True,  # 自动混合精度
        'single_cls': False,
        'project': 'high_precision_detection',
        'name': 'yolo11s_ocr_integrated',
        'save': True,
        'save_period': 3,  # 每10个epoch保存一次
        'val': True,
        'plots': True,
        'verbose': True,
        # 数据增强参数 - 提高模型泛化能力
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
    }

    # 开始训练
    results = model.train(**training_args)

    print("✅ YOLO模型训练完成!")
    return model, results


class TrueIntegratedYOLOOCRModel(nn.Module):
    """
    真正整合的YOLO+OCR模型：将YOLO目标检测和OCR文字检测整合到一个PyTorch模型中
    这个模型可以保存为单个.pt文件，同时进行目标检测和文字检测
    """
    def __init__(self, yolo_model_path: str, num_classes: int = 47):
        super(TrueIntegratedYOLOOCRModel, self).__init__()

        print(f"🔧 创建真正整合的YOLO+OCR模型，使用YOLO权重: {yolo_model_path}")

        # 存储模型路径以便保存时使用
        self.yolo_model_path = yolo_model_path

        # 加载预训练YOLO模型
        self.yolo_model = YOLO(yolo_model_path)
        self.num_classes = num_classes

        # 初始化OCR引擎
        self.init_ocr_engines()
        
        # 初始化外部OCR引擎作为备用
        self.init_external_ocr_engines()

        # 设置检测阈值
        self.detection_conf_threshold = 0.15
        self.ocr_confidence_threshold = 0.05

        print("✅ 真正整合YOLO+OCR模型初始化完成")
        print(f"   🎯 YOLO检测阈值: {self.detection_conf_threshold}")
        print(f"   📝 OCR置信度阈值: {self.ocr_confidence_threshold}")
        print(f"   🔧 目标类别数: {self.num_classes}")

    def init_ocr_engines(self):
        """初始化OCR引擎"""
        self.ocr_engines = {}

        # EasyOCR引擎 - 支持中英文
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR引擎初始化失败: {e}")

        # CnOCR引擎 - 高精度中文识别
        if CNOCR_AVAILABLE:
            try:
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',
                    det_model_name='db_resnet18',
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR引擎初始化失败: {e}")

        # Tesseract引擎
        if TESSERACT_AVAILABLE:
            try:
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = pytesseract
                print("✓ Tesseract引擎初始化成功")
            except Exception as e:
                print(f"✗ Tesseract引擎初始化失败: {e}")

        print(f"📊 已初始化 {len(self.ocr_engines)} 个OCR引擎: {list(self.ocr_engines.keys())}")

    def init_external_ocr_engines(self):
        """初始化外部OCR引擎作为备用"""
        self.external_ocr_engines = {}

        # EasyOCR引擎 - 支持中英文
        if EASYOCR_AVAILABLE:
            try:
                self.external_ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR备用引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR备用引擎初始化失败: {e}")

        # CnOCR引擎 - 高精度中文识别
        if CNOCR_AVAILABLE:
            try:
                self.external_ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',
                    det_model_name='db_resnet18',
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR备用引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR备用引擎初始化失败: {e}")

        print(f"📊 已初始化 {len(self.external_ocr_engines)} 个备用OCR引擎")

    def forward(self, x):
        """
        前向传播：这里主要用于兼容PyTorch模型接口
        实际推理使用predict方法
        """
        # 使用YOLO模型进行前向传播
        return self.yolo_model.model(x)

    def predict(self, image_path: str, save_result: bool = True, output_dir: str = 'results'):
        """
        整合预测：同时进行YOLO目标检测和OCR文字识别
        """
        print(f"🎯 使用整合模型进行预测: {image_path}")

        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None

        # 1. YOLO目标检测
        print("🎯 进行YOLO目标检测...")
        yolo_results = self.yolo_model.predict(
            image_path,
            conf=self.detection_conf_threshold,
            save=False,
            verbose=False
        )

        # 2. OCR文字识别
        print("📝 进行OCR文字识别...")
        text_results = []

        # 使用多个OCR引擎进行文字识别
        if 'cnocr' in self.ocr_engines:
            try:
                cnocr_results = self.ocr_engines['cnocr'].ocr(image)
                for result in cnocr_results:
                    if result['text'].strip():  # 过滤空文本
                        text_results.append({
                            'text': result['text'],
                            'confidence': result.get('score', 0.0),
                            'detection_bbox': [
                                int(result['position'][0][0]), int(result['position'][0][1]),
                                int(result['position'][2][0]), int(result['position'][2][1])
                            ],
                            'engine': 'cnocr'
                        })
            except Exception as e:
                print(f"CnOCR识别失败: {e}")

        if 'easyocr' in self.ocr_engines:
            try:
                easyocr_results = self.ocr_engines['easyocr'].readtext(image)
                for bbox, text, confidence in easyocr_results:
                    if text.strip() and confidence > self.ocr_confidence_threshold:
                        # 转换bbox格式
                        x_coords = [point[0] for point in bbox]
                        y_coords = [point[1] for point in bbox]
                        x1, y1 = int(min(x_coords)), int(min(y_coords))
                        x2, y2 = int(max(x_coords)), int(max(y_coords))

                        text_results.append({
                            'text': text,
                            'confidence': confidence,
                            'detection_bbox': [x1, y1, x2, y2],
                            'engine': 'easyocr'
                        })
            except Exception as e:
                print(f"EasyOCR识别失败: {e}")

        # 去重和过滤
        text_results = self.filter_and_deduplicate_text_results(text_results)

        # 构建最终结果
        final_result = {
            'image_path': image_path,
            'detections': yolo_results,
            'text_recognition': text_results,
            'model_type': 'TrueIntegratedYOLOOCR',
            'detection_count': len(yolo_results[0].boxes) if yolo_results and yolo_results[0].boxes is not None else 0,
            'text_count': len(text_results),
            'timestamp': time.time()
        }

        print(f"✅ 预测完成:")
        print(f"   🎯 检测到目标: {final_result['detection_count']} 个")
        print(f"   📝 识别到文字: {final_result['text_count']} 段")

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result

    def filter_and_deduplicate_text_results(self, text_results):
        """去重和过滤文字识别结果"""
        if not text_results:
            return []

        # 按置信度排序
        text_results.sort(key=lambda x: x['confidence'], reverse=True)

        # 去重：移除重叠度高的检测框
        filtered_results = []
        for result in text_results:
            is_duplicate = False
            for existing in filtered_results:
                if self.bbox_similarity(result['detection_bbox'], existing['detection_bbox']) > 0.7:
                    is_duplicate = True
                    break

            if not is_duplicate and result['confidence'] > self.ocr_confidence_threshold:
                filtered_results.append(result)

        return filtered_results

    def bbox_similarity(self, bbox1, bbox2):
        """计算两个边界框的相似度（IoU）"""
        try:
            x1_1, y1_1, x2_1, y2_1 = bbox1
            x1_2, y1_2, x2_2, y2_2 = bbox2

            # 计算交集
            x1_inter = max(x1_1, x1_2)
            y1_inter = max(y1_1, y1_2)
            x2_inter = min(x2_1, x2_2)
            y2_inter = min(y2_1, y2_2)

            if x2_inter <= x1_inter or y2_inter <= y1_inter:
                return 0.0

            intersection = (x2_inter - x1_inter) * (y2_inter - y1_inter)

            # 计算并集
            area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
            area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
            union = area1 + area2 - intersection

            return intersection / union if union > 0 else 0.0
        except:
            return 0.0

    def save_integrated_model(self, save_path: str):
        """
        保存整合模型到.pt文件
        """
        print(f"💾 保存真正整合的YOLO+OCR模型到: {save_path}")

        # 创建保存目录
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 临时移除不可序列化的OCR引擎
        ocr_engines_backup = self.ocr_engines
        external_ocr_engines_backup = getattr(self, 'external_ocr_engines', {})
        self.ocr_engines = {}
        self.external_ocr_engines = {}

        try:
            # 保存模型状态字典和配置信息
            model_data = {
                'model_state_dict': self.state_dict(),
                'model_type': 'TrueIntegratedYOLOOCR',
                'num_classes': self.num_classes,
                'detection_conf_threshold': self.detection_conf_threshold,
                'ocr_confidence_threshold': self.ocr_confidence_threshold,
                'yolo_model_path': getattr(self, 'yolo_model_path', None),
                'yolo_model': self.yolo_model  # 直接保存YOLO模型
            }
            torch.save(model_data, save_path)
            print(f"✅ 整合模型已保存到: {save_path}")
            print(f"   📝 包含了完整的YOLO模型，无需再次加载YOLO权重")
        finally:
            # 恢复OCR引擎
            self.ocr_engines = ocr_engines_backup
            self.external_ocr_engines = external_ocr_engines_backup

        return save_path

    def save_prediction_result(self, result, image, output_dir):
        """保存预测结果"""
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 获取图像文件名
            image_name = os.path.splitext(os.path.basename(result['image_path']))[0]

            # 绘制结果图像
            result_image = image.copy()

            # 绘制YOLO检测结果（绿色框）
            if result['detections'] and len(result['detections']) > 0:
                detection_result = result['detections'][0]
                if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                    for box in detection_result.boxes:
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)  # 绿色框

                        # 添加类别标签
                        if hasattr(box, 'cls') and hasattr(detection_result, 'names'):
                            class_id = int(box.cls[0])
                            class_name = detection_result.names.get(class_id, f'Class_{class_id}')
                            confidence = float(box.conf[0])
                            label = f'{class_name}: {confidence:.2f}'
                            cv2.putText(result_image, label, (x1, y1-10),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

            # 绘制OCR文字识别结果（蓝色框）
            for text_result in result['text_recognition']:
                x1, y1, x2, y2 = text_result['detection_bbox']
                cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 0), 2)  # 蓝色框

                # 使用PIL绘制中文文字
                result_image = self.draw_chinese_text(
                    result_image, text_result['text'], (x1, y1-25),
                    font_size=16, color=(255, 0, 0)
                )

            # 保存结果图像
            result_image_path = os.path.join(output_dir, f"{image_name}_result.jpg")
            cv2.imwrite(result_image_path, result_image)

            # 保存JSON结果
            result_json_path = os.path.join(output_dir, f"{image_name}_result.json")
            with open(result_json_path, 'w', encoding='utf-8') as f:
                import json
                # 转换结果为可序列化格式
                serializable_result = {
                    'image_path': result['image_path'],
                    'detection_count': result['detection_count'],
                    'text_count': result['text_count'],
                    'text_recognition': result['text_recognition'],
                    'model_type': result['model_type'],
                    'timestamp': result['timestamp']
                }
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)

            print(f"💾 结果已保存:")
            print(f"   📸 图像: {result_image_path}")
            print(f"   📄 JSON: {result_json_path}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def draw_chinese_text(self, image, text, position, font_size=20, color=(255, 0, 0)):
        """使用PIL绘制中文文字"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import numpy as np

            # 转换为PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)

            # 尝试加载中文字体
            try:
                font = ImageFont.truetype("simhei.ttf", font_size)
            except:
                try:
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    font = ImageFont.load_default()

            # 绘制文字
            draw.text(position, text, font=font, fill=color)

            # 转换回OpenCV格式
            return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"绘制中文文字失败: {e}")
            # 回退到OpenCV文字绘制
            cv2.putText(image, text.encode('utf-8').decode('utf-8', 'ignore'),
                       position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 1)
            return image

    @classmethod
    def load_integrated_model(cls, model_path: str, yolo_model_path: str = None):
        """
        从.pt文件加载整合模型
        """
        print(f"📂 加载整合模型: {model_path}")

        try:
            # 加载模型数据
            model_data = torch.load(model_path, map_location='cpu')

            # 检查是否是我们的新整合模型格式
            if isinstance(model_data, dict) and 'model_type' in model_data and model_data['model_type'] == 'TrueIntegratedYOLOOCR':
                # 新格式：从保存的配置重建模型
                saved_yolo_path = model_data.get('yolo_model_path', yolo_model_path)
                
                # 如果模型数据中包含了完整的YOLO模型，直接使用它
                if 'yolo_model' in model_data and model_data['yolo_model'] is not None:
                    # 创建新的模型实例，但不加载YOLO模型
                    model = cls.__new__(cls)
                    nn.Module.__init__(model)
                    
                    # 设置基本属性
                    model.num_classes = model_data.get('num_classes', 47)
                    model.yolo_model = model_data['yolo_model']
                    model.detection_conf_threshold = model_data.get('detection_conf_threshold', 0.15)
                    model.ocr_confidence_threshold = model_data.get('ocr_confidence_threshold', 0.05)
                    
                    # 初始化OCR引擎
                    model.init_ocr_engines()
                    
                    # 初始化外部OCR引擎
                    model.init_external_ocr_engines()
                    
                    print(f"✅ 成功加载整合模型 (包含YOLO模型)")
                    return model
                
                # 如果没有保存YOLO模型但有路径，使用路径加载
                elif saved_yolo_path is not None:
                    # 创建新的模型实例
                    model = cls(saved_yolo_path, model_data.get('num_classes', 47))
                    
                    # 加载模型状态
                    model.load_state_dict(model_data['model_state_dict'])
                    
                    # 恢复配置
                    model.detection_conf_threshold = model_data.get('detection_conf_threshold', 0.15)
                    model.ocr_confidence_threshold = model_data.get('ocr_confidence_threshold', 0.05)
                    
                    print(f"✅ 成功加载整合模型 (使用YOLO路径: {saved_yolo_path})")
                    return model
                else:
                    raise ValueError("模型文件中没有包含YOLO模型，也没有提供yolo_model_path参数")

            # 检查是否是旧的整合模型格式
            elif 'model_type' in model_data and model_data['model_type'] == 'IntegratedYOLOOCR':
                # 如果保存了完整模型实例，直接返回
                if 'model' in model_data and model_data['model'] is not None:
                    model = model_data['model']
                    print(f"✅ 从完整模型实例加载成功")
                    return model

                # 否则重新创建模型并加载权重
                if yolo_model_path is None:
                    yolo_model_path = 'yolo11s.pt'

                model = cls(
                    yolo_model_path=yolo_model_path,
                    num_classes=model_data.get('num_classes', 47),
                    ocr_vocab_size=model_data.get('ocr_vocab_size', 6000)
                )

                # 加载模型权重
                if 'state_dict' in model_data:
                    model.load_state_dict(model_data['state_dict'])

                # 恢复阈值设置
                model.detection_conf_threshold = model_data.get('detection_conf_threshold', 0.15)
                model.ocr_confidence_threshold = model_data.get('ocr_confidence_threshold', 0.05)
                model.text_detection_threshold = model_data.get('text_detection_threshold', 0.3)

                print(f"✅ 整合模型加载完成")
                print(f"   📊 模型版本: {model_data.get('version', 'unknown')}")
                print(f"   🎯 目标类别数: {model.num_classes}")
                print(f"   📚 OCR词汇表大小: {model.ocr_vocab_size}")

                return model
            else:
                print(f"❌ 不是有效的整合模型文件")
                return None

        except Exception as e:
            print(f"❌ 加载整合模型失败: {e}")
            return None

    def predict_with_integrated_model(self, image_path: str, save_result: bool = True, output_dir: str = 'results'):
        """
        使用整合模型进行预测（兼容原有的predict接口）
        """
        print(f"🎯 使用整合模型进行预测: {image_path}")

        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None

        # 使用YOLO进行目标检测
        yolo_results = self.yolo_model.predict(
            image_path,
            conf=self.detection_conf_threshold,
            save=False,
            verbose=False
        )

        # 使用外部OCR引擎进行文字识别（作为备用）
        text_results = []
        if hasattr(self, 'external_ocr_engines') and self.external_ocr_engines:
            # 使用CnOCR
            if 'cnocr' in self.external_ocr_engines:
                try:
                    cnocr_results = self.external_ocr_engines['cnocr'].ocr(image)
                    for result in cnocr_results:
                        text_results.append({
                            'text': result['text'],
                            'confidence': result.get('score', 0.0),
                            'detection_bbox': [
                                int(result['position'][0][0]), int(result['position'][0][1]),
                                int(result['position'][2][0]), int(result['position'][2][1])
                            ],
                            'engine': 'cnocr'
                        })
                except Exception as e:
                    print(f"CnOCR识别失败: {e}")

        # 构建结果
        final_result = {
            'image_path': image_path,
            'detections': yolo_results,
            'text_recognition': text_results,
            'model_type': 'IntegratedYOLOOCR',
            'timestamp': time.time()
        }

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result


def create_multitask_model(yolo_model_path=None):
    """
    创建多任务模型 (YOLO目标检测 + CnOCR文字识别)
    """
    print("🚀 创建多任务模型...")

    # 创建多任务模型
    if yolo_model_path is None:
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    model = MultiTaskModel(yolo_model_path)

    print("✅ 多任务模型创建完成!")
    print("📝 使用YOLO进行目标检测 + CnOCR进行文字识别")

    return model


def create_integrated_model(yolo_model_path=None, save_path=None):
    """
    创建真正整合的YOLO+OCR模型并保存为单个.pt文件
    这个模型可以同时进行YOLO目标检测（绿色框）和OCR文字识别（蓝色框）
    """
    if yolo_model_path is None:
        # 使用训练好的最佳模型
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'

        # 如果不存在，使用预训练模型
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    print(f"🔧 创建真正整合的YOLO+OCR模型，使用YOLO权重: {yolo_model_path}")
    print("📝 这是一个真正的整合模型，保存为单个.pt文件")

    # 创建真正整合的模型
    integrated_model = TrueIntegratedYOLOOCRModel(yolo_model_path)

    # 保存整合模型
    if save_path is None:
        save_path = 'models/integrated_yolo_ocr_model.pt'

    integrated_model.save_integrated_model(save_path)

    print("✅ 真正整合的YOLO+OCR模型创建并保存完成!")
    print(f"📁 模型保存到: {save_path}")
    print("📝 使用说明:")
    print("   - 这是一个真正的整合模型，保存为单个.pt文件")
    print("   - 可以同时进行YOLO目标检测（绿色框）和OCR文字识别（蓝色框）")
    print("   - 推理时只需要加载这一个模型文件")
    print("   - 支持CnOCR、EasyOCR等多个OCR引擎")

    return integrated_model, save_path

def load_integrated_model(model_path: str):
    """
    加载整合的YOLO+OCR模型
    """
    print(f"📂 加载整合模型: {model_path}")

    try:
        # 检查是否是JSON配置文件
        if model_path.endswith('.json'):
            import json
            with open(model_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            if config.get('model_type') == 'MultiTaskWrapper':
                print("🔧 加载多任务包装模型...")
                yolo_model_path = config['yolo_model_path']

                # 创建多任务模型
                model = MultiTaskModel(yolo_model_path)

                # 恢复配置
                model.detection_conf_threshold = config.get('detection_conf_threshold', 0.15)
                model.ocr_confidence_threshold = config.get('ocr_confidence_threshold', 0.05)

                print(f"✅ 多任务包装模型加载完成")
                print(f"   🎯 YOLO模型: {yolo_model_path}")
                print(f"   📝 OCR引擎: {config.get('available_ocr_engines', [])}")

                return model
            else:
                print(f"❌ 不支持的模型类型: {config.get('model_type')}")
                return None
        else:
            # 尝试作为PyTorch模型加载
            return TrueIntegratedYOLOOCRModel.load_integrated_model(model_path)

    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return None

def create_legacy_multitask_model(yolo_model_path=None):
    """
    创建传统多任务模型 (YOLO目标检测 + 外部OCR文字识别)
    """
    if yolo_model_path is None:
        # 使用训练好的最佳模型
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'

        # 如果不存在，使用预训练模型
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    print(f"🔧 创建传统多任务模型，使用YOLO权重: {yolo_model_path}")

    # 创建多任务模型
    legacy_model = MultiTaskModel(yolo_model_path)

    print("✅ 传统多任务模型创建完成!")
    return legacy_model


def test_integrated_model(model, test_images_dir='yqjdataset/test/images', output_dir='integrated_results'):
    """
    测试整合模型的性能
    """
    print(f"🧪 开始测试整合模型...")

    # 获取测试图像
    test_images = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        test_images.extend(Path(test_images_dir).glob(ext))

    if not test_images:
        print(f"❌ 在 {test_images_dir} 中未找到测试图像")
        return

    print(f"📊 找到 {len(test_images)} 张测试图像")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试模型
    results = []
    for i, image_path in enumerate(tqdm(test_images[:10], desc="测试进度")):  # 测试前10张图像
        try:
            result = model.predict(str(image_path), save_result=True, output_dir=output_dir)
            results.append(result)

            # 打印结果摘要
            detection_count = 0
            if len(result['detections']) > 0:
                detection_result = result['detections'][0]
                if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                    detection_count = len(detection_result.boxes)
            text_count = len(result['text_recognition'])

            print(f"图像 {i+1}: 检测到 {detection_count} 个目标, 识别到 {text_count} 段文字")

        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")

    print(f"✅ 测试完成! 结果保存在 {output_dir}")
    return results


def main():
    """
    主函数：训练和测试整合模型
    """
    print("🎯 高精度目标检测+OCR文字识别整合训练系统")
    print("=" * 60)
    print("📋 架构说明:")
    print("   输入图像 -> 主干网络(Backbone) -> 特征金字塔(Neck)")
    print("   ├── 元器件检测头(YOLO head) -> 预测元器件类别和框")
    print("   └── 文字检测+识别头(OCR head) -> 预测文字框和字符内容")
    print("=" * 60)

    # 检查是否已有保存的整合模型
    model_path = 'models/integrated_yolo_ocr_model.pt'
    if os.path.exists(model_path):
        print(f"\n📍 发现已保存的整合模型: {model_path}")
        print(f"   📝 直接加载整合模型进行测试...")
        
        # 加载整合模型
        integrated_model = load_integrated_model(model_path)
        
        if integrated_model is not None:
            print("✅ 整合模型加载成功")
            
            # 测试整合模型
            print("\n📍 测试整合模型性能")
            try:
                _ = test_integrated_model(integrated_model)
                print("✅ 模型测试完成")
            except Exception as e:
                print(f"⚠️ 模型测试出错: {e}")
            
            return integrated_model
        else:
            print("❌ 整合模型加载失败，将创建新模型")

    # 步骤1: 训练YOLO模型
    print("\n📍 步骤1: 训练高精度YOLO目标检测模型")
    try:
        _, _ = train_yolo_model()
        print("✅ YOLO模型训练完成")
    except Exception as e:
        print(f"⚠️ YOLO训练出错，使用预训练模型: {e}")

    # 步骤2: 创建多任务整合模型
    print("\n📍 步骤2: 创建多任务整合模型 (目标检测+OCR)")
    integrated_model, save_path = create_integrated_model()
    print(f"📁 整合模型配置已保存到: {save_path}")

    # 步骤3: 创建多任务模型 (可选)
    print("\n📍 步骤3: 多任务模型创建")
    try:
        _ = create_multitask_model()
        print("✅ 多任务模型创建完成")
    except Exception as e:
        print(f"⚠️ 多任务模型创建出错: {e}")

    # 步骤4: 测试整合模型
    print("\n📍 步骤4: 测试整合模型性能")
    try:
        _ = test_integrated_model(integrated_model)
        print("✅ 模型测试完成")
    except Exception as e:
        print(f"⚠️ 模型测试出错: {e}")

    print("\n🎉 训练和测试完成!")
    print("📁 检查以下目录获取结果:")
    print("   - high_precision_detection/yolo11s_ocr_integrated/ (YOLO训练结果)")
    print("   - integrated_results/ (整合模型测试结果)")
    print("\n🔧 模型特点:")
    print("   ✓ 共享主干网络，提高计算效率")
    print("   ✓ 双分支输出：目标检测 + 文字识别")
    print("   ✓ 多OCR引擎融合，提高文字识别精度")
    print("   ✓ 端到端训练能力 (需要相应标注数据)")
    print("   ✓ 真正整合模型，无需再次加载YOLO权重")

    return integrated_model


def demo_multitask_prediction(image_path='DaYuanTuZ_0.png'):
    """
    演示多任务模型的预测功能
    """
    print(f"🎬 演示多任务模型预测功能")
    print(f"📸 测试图像: {image_path}")

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return

    # 加载整合模型
    model_path = 'models/integrated_yolo_ocr_model.pt'
    if os.path.exists(model_path):
        print(f"📂 加载已保存的整合模型: {model_path}")
        model = load_integrated_model(model_path)
    else:
        print(f"⚠️ 未找到保存的整合模型，创建新模型")
        model, _ = create_integrated_model()

    # 进行预测
    try:
        result = model.predict(image_path, save_result=True, output_dir='demo_results')

        # 打印结果摘要
        detection_count = 0
        if len(result['detections']) > 0:
            detection_result = result['detections'][0]
            if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                detection_count = len(detection_result.boxes)
        text_count = len(result['text_recognition'])
        cnocr_text_regions = result.get('cnocr_text_regions', 0)
        detection_text_regions = result.get('detection_text_regions', 0)

        print(f"\n📊 预测结果摘要:")
        print(f"   🎯 检测到元器件: {detection_count} 个")
        print(f"   📝 识别到文字: {text_count} 段")
        print(f"   🧠 CnOCR文字区域: {cnocr_text_regions} 个")
        print(f"   🔍 检测框文字区域: {detection_text_regions} 个")

        if text_count > 0:
            print("\n📝 识别到的文字:")
            for i, text_result in enumerate(result['text_recognition'][:5]):  # 显示前5个
                print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")
            if text_count > 5:
                print(f"   ... 以及 {text_count - 5} 个更多文字区域")

        print(f"\n💾 结果已保存到 demo_results 目录")
        return result

    except Exception as e:
        print(f"❌ 预测失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == '__main__':
    import sys
    import argparse

    parser = argparse.ArgumentParser(description='YOLO+OCR整合模型训练和预测')
    parser.add_argument('--mode', type=str, default='train',
                       choices=['train', 'predict', 'create_integrated', 'demo'],
                       help='运行模式: train(训练), predict(预测), create_integrated(创建整合模型), demo(演示)')
    parser.add_argument('--image', type=str, default='DaYuanTuZ_0.png',
                       help='预测时使用的图像路径')
    parser.add_argument('--yolo_model', type=str, default=None,
                       help='YOLO模型路径')
    parser.add_argument('--save_path', type=str, default='models/integrated_yolo_ocr_model.pt',
                       help='整合模型保存路径')
    parser.add_argument('--load_model', type=str, default=None,
                       help='加载已保存的整合模型路径')

    args = parser.parse_args()

    if args.mode == 'train':
        # 完整训练模式
        print("🚀 开始完整训练流程...")
        model = main()

    elif args.mode == 'create_integrated':
        # 创建整合模型模式
        print("🔧 创建整合YOLO+OCR模型...")
        integrated_model, save_path = create_integrated_model(
            yolo_model_path=args.yolo_model,
            save_path=args.save_path
        )
        print(f"✅ 整合模型已创建并保存到: {save_path}")
        print("📝 现在您可以使用以下命令进行预测:")
        print(f"   python train.py --mode predict --load_model {save_path} --image your_image.png")

    elif args.mode == 'predict':
        # 预测模式
        print(f"🎯 预测模式，图像: {args.image}")

        if args.load_model:
            # 使用保存的整合模型
            print(f"📂 加载整合模型: {args.load_model}")
            model = load_integrated_model(args.load_model)
        else:
            # 使用传统多任务模型
            print("🔧 创建传统多任务模型...")
            model = create_legacy_multitask_model(args.yolo_model)

        # 进行预测
        if not os.path.exists(args.image):
            print(f"❌ 图像文件不存在: {args.image}")
        else:
            try:
                result = model.predict(args.image, save_result=True, output_dir='demo_results')

                # 打印结果摘要
                detection_count = 0
                if len(result['detections']) > 0:
                    detection_result = result['detections'][0]
                    if hasattr(detection_result, 'boxes') and detection_result.boxes is not None:
                        detection_count = len(detection_result.boxes)
                text_count = len(result['text_recognition'])

                print(f"\n📊 预测结果摘要:")
                print(f"   🎯 检测到元器件: {detection_count} 个")
                print(f"   📝 识别到文字: {text_count} 段")

                if text_count > 0:
                    print(f"\n📝 识别到的文字内容:")
                    for i, text_result in enumerate(result['text_recognition'][:5]):
                        print(f"   {i+1}. {text_result['text']} (置信度: {text_result['confidence']:.2f}, 引擎: {text_result['engine']})")

                print(f"\n💾 结果已保存到: demo_results/")

            except Exception as e:
                import traceback
                print(f"❌ 预测过程出错: {e}")
                print(f"错误详情: {traceback.format_exc()}")

    elif args.mode == 'demo':
        # 演示模式
        demo_multitask_prediction(args.image)

    else:
        print(f"❌ 未知模式: {args.mode}")
        parser.print_help()